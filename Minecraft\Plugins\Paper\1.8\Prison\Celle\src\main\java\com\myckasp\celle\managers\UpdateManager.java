package com.myckasp.celle.managers;

import com.myckasp.celle.Celle;
import com.myckasp.celle.utils.UpdateChecker;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;

public class UpdateManager {
    
    private final Celle plugin;
    private final UpdateChecker updateChecker;
    private boolean updateAvailable = false;
    private String latestVersion;
    
    public UpdateManager(Celle plugin) {
        this.plugin = plugin;
        this.updateChecker = new UpdateChecker(plugin);
    }
    
    public void checkForUpdates() {
        plugin.getLogger().info(plugin.getLanguageManager().getMessage("console.update-checking"));
        updateChecker.checkForUpdates().thenAccept(hasUpdate -> {
            this.updateAvailable = hasUpdate;
            if (hasUpdate) {
                this.latestVersion = updateChecker.getLatestVersion();
                plugin.getLogger().info(plugin.getLanguageManager().getMessage("console.update-available")
                        .replace("%version%", latestVersion));
                plugin.getLogger().info(plugin.getLanguageManager().getMessage("console.update-download"));
            } else {
                plugin.getLogger().info(plugin.getLanguageManager().getMessage("console.update-up-to-date"));
            }
        });
    }
    
    public void checkForUpdatesAsync() {
        new BukkitRunnable() {
            @Override
            public void run() {
                checkForUpdates();
            }
        }.runTaskAsynchronously(plugin);
    }
    
    public void checkForUpdatesAndNotify(CommandSender sender) {
        new BukkitRunnable() {
            @Override
            public void run() {
                checkForUpdates();
                // Notify the sender after the async check completes
                notifyUpdateAvailable(sender);
            }
        }.runTaskAsynchronously(plugin);
    }
    
    public void notifyUpdateAvailable(CommandSender sender) {
        if (updateAvailable && latestVersion != null) {
            sender.sendMessage(plugin.getLanguageManager().getMessage("update.available")
                    .replace("%current%", plugin.getDescription().getVersion())
                    .replace("%latest%", latestVersion));
            sender.sendMessage(plugin.getLanguageManager().getMessage("update.download"));
        } else {
            sender.sendMessage(plugin.getLanguageManager().getMessage("update.not-available"));
        }
    }
    
    public boolean isUpdateAvailable() {
        return updateAvailable;
    }
    
    public String getLatestVersion() {
        return latestVersion;
    }
    
    public void shutdown() {
        updateChecker.shutdown();
    }
}